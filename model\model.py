#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
###########################################################################
# Copyright © 1998 - 2025 Tencent. All Rights Reserved.
###########################################################################
"""
Author: Tencent AI Arena Authors
"""


from typing import List
import torch
from torch import nn
import numpy as np
from agent_ppo.conf.conf import Config

import sys
import os

if os.path.basename(sys.argv[0]) == "learner.py":
    import torch

    torch.set_num_interop_threads(2)
    torch.set_num_threads(2)
else:
    import torch

    torch.set_num_interop_threads(4)
    torch.set_num_threads(4)


class NetworkModelBase(nn.Module):
    def __init__(self):
        super().__init__()
        # feature configure parameter
        # 特征配置参数
        self.data_split_shape = Config.DATA_SPLIT_SHAPE
        self.feature_split_shape = Config.FEATURE_SPLIT_SHAPE
        self.label_size = Config.ACTION_NUM
        self.feature_len = Config.FEATURE_LEN
        self.value_num = Config.VALUE_NUM

        self.var_beta = Config.BETA_START
        self.vf_coef = Config.VF_COEF

        self.clip_param = Config.CLIP_PARAM

        self.data_len = Config.data_len

        # CNN for local vision features (11x11 grids)
        # CNN处理局部视野特征 (11x11网格)
        self.vision_cnn = nn.Sequential(
            nn.Conv2d(5, 16, kernel_size=3, padding=1),  # 5 channels: treasure, obstacle, buff, end, memory
            nn.ReLU(),
            nn.Conv2d(16, 32, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.Conv2d(32, 16, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.AdaptiveAvgPool2d((5, 5))  # Reduce to 5x5
        )

        # MLP for CNN output
        # CNN输出的MLP处理
        self.vision_mlp = MLP([16 * 5 * 5, 128, 64], "vision_mlp", non_linearity_last=True)

        # Vector feature length
        # 向量特征长度
        self.vector_feature_len = Config.VECTOR_FEATURE_LEN

        # Main MLP network for combined features
        # 主MLP网络处理组合特征
        self.main_fc_dim_list = [Config.PROCESSED_FEATURE_LEN, 128, 256]
        self.main_mlp_net = MLP(self.main_fc_dim_list, "main_mlp_net", non_linearity_last=True)
        self.label_mlp = MLP([256, 64, self.label_size], "label_mlp")
        self.value_mlp = MLP([256, 64, self.value_num], "value_mlp")

    def process_legal_action(self, label, legal_action):
        label_max, _ = torch.max(label * legal_action, 1, True)
        label = label - label_max
        label = label * legal_action
        label = label + 1e5 * (legal_action - 1)
        return label

    def forward(self, vector_features, map_features, legal_action):
        # Process separated features
        # 处理分离的特征
        batch_size = vector_features.shape[0]

        # CNN processing for map features
        # CNN处理地图特征
        # map_features shape: (batch_size, 5, 11, 11)
        vision_cnn_out = self.vision_cnn(map_features)  # Shape: (batch_size, 16, 5, 5)
        vision_cnn_flat = vision_cnn_out.view(batch_size, -1)  # Shape: (batch_size, 16*5*5)

        # MLP processing for CNN output
        # MLP处理CNN输出
        vision_mlp_out = self.vision_mlp(vision_cnn_flat)  # Shape: (batch_size, 64)

        # Combine vector features and processed vision features
        # 组合向量特征和处理后的视野特征
        combined_features = torch.cat([vector_features, vision_mlp_out], dim=1)

        # Main MLP processing
        # 主MLP处理
        fc_mlp_out = self.main_mlp_net(combined_features)

        # Action and value processing
        # 处理动作和值
        label_mlp_out = self.label_mlp(fc_mlp_out)
        label_out = self.process_legal_action(label_mlp_out, legal_action)

        prob = torch.nn.functional.softmax(label_out, dim=1)
        value = self.value_mlp(fc_mlp_out)

        return prob, value


class NetworkModelActor(NetworkModelBase):
    def format_data(self, vector_features, map_features, legal_action):
        return (
            torch.tensor(vector_features).to(torch.float32),
            torch.tensor(map_features).to(torch.float32),
            torch.tensor(legal_action).to(torch.float32),
        )


class NetworkModelLearner(NetworkModelBase):
    def format_data(self, datas):
        return datas.view(-1, self.data_len).float().split(self.data_split_shape, dim=1)

    def forward(self, data_list, inference=False):
        # Extract features from data_list
        # 从data_list中提取特征
        combined_features = data_list[0]  # This contains both vector and map features
        legal_action = data_list[-1]

        # Split combined features into vector and map parts
        # 将组合特征分割为向量和地图部分
        batch_size = combined_features.shape[0]
        vector_features = combined_features[:, :Config.VECTOR_FEATURE_LEN]  # First 368 dims
        map_features_flat = combined_features[:, Config.VECTOR_FEATURE_LEN:]  # Remaining dims

        # Reshape map features to (batch_size, 5, 11, 11)
        # 重塑地图特征为 (batch_size, 5, 11, 11)
        map_features = map_features_flat.view(batch_size, Config.MAP_FEATURE_CHANNELS,
                                            Config.MAP_FEATURE_SIZE, Config.MAP_FEATURE_SIZE)

        return super().forward(vector_features, map_features, legal_action)


def make_fc_layer(in_features: int, out_features: int):
    # Wrapper function to create and initialize a linear layer
    # 创建并初始化一个线性层
    fc_layer = nn.Linear(in_features, out_features)

    # initialize weight and bias
    # 初始化权重及偏移量
    nn.init.orthogonal(fc_layer.weight)
    nn.init.zeros_(fc_layer.bias)

    return fc_layer


class MLP(nn.Module):
    def __init__(
        self,
        fc_feat_dim_list: List[int],
        name: str,
        non_linearity: nn.Module = nn.ReLU,
        non_linearity_last: bool = False,
    ):
        # Create a MLP object
        # 创建一个 MLP 对象
        super().__init__()
        self.fc_layers = nn.Sequential()
        for i in range(len(fc_feat_dim_list) - 1):
            fc_layer = make_fc_layer(fc_feat_dim_list[i], fc_feat_dim_list[i + 1])
            self.fc_layers.add_module("{0}_fc{1}".format(name, i + 1), fc_layer)
            # no relu for the last fc layer of the mlp unless required
            # 除非有需要，否则 mlp 的最后一个 fc 层不使用 relu
            if i + 1 < len(fc_feat_dim_list) - 1 or non_linearity_last:
                self.fc_layers.add_module("{0}_non_linear{1}".format(name, i + 1), non_linearity())

    def forward(self, data):
        return self.fc_layers(data)
