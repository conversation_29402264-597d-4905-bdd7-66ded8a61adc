#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
###########################################################################
# Copyright © 1998 - 2025 Tencent. All Rights Reserved.
###########################################################################

"""
Author: Tencent AI Arena Authors

"""

import numpy as np
import math
from agent_ppo.feature.definition import RelativeDistance, RelativeDirection, DirectionAngles, reward_process


def norm(v, max_v, min_v=0):
    v = np.maximum(np.minimum(max_v, v), min_v)
    return (v - min_v) / (max_v - min_v)


class Preprocessor:
    def __init__(self) -> None:
        # 从8修改成16，代表8个移动维度，8个闪现维度
        self.move_action_num = 16
        self.reset()

    def reset(self):
        self.step_no = 0
        self.cur_pos = (0, 0)
        self.cur_pos_norm = np.array((0, 0))
        self.end_pos = None
        self.is_end_pos_found = False
        self.history_pos = []
        self.bad_move_ids = set()
        # 闪现是否可用，初始化为True
        self.is_flashed = True
        # 初始化记忆矩阵
        self.global_memory_map = np.zeros((128,128), dtype=np.float32)
        self.local_memory_map = np.zeros((11,11), dtype=np.float32)

        # 初始化organs特征系统
        self._reset_organs_features()

    def _reset_organs_features(self):
        """重置organs原始特征"""
        # 原始organs特征矩阵：每个organ的正确编码特征
        # 最多15个organs，每个organ包含：
        # - 3维 sub_type one-hot: [is_treasure, is_buff, is_end]
        # - 3维 status one-hot: [is_out_of_sight, is_cooling, is_available]
        # - 2维 归一化位置: [norm_pos_x, norm_pos_z]
        # - 1维 归一化冷却: [norm_cooldown]
        # - 6维 relative_distance one-hot: [VerySmall, Small, Medium, Large, VeryLarge, Unknown]
        # - 9维 relative_direction one-hot: [North, NorthEast, East, SouthEast, South, SouthWest, West, NorthWest, Unknown]
        # 总计24维每个organ
        self.organs_features = np.zeros((15, 24), dtype=np.float32)

    def memory_update(self, cur_pos):
        """
        记忆矩阵更新
        """
        # 全局记忆矩阵
        x,z = cur_pos
        z = 127 - z
        current_value = self.global_memory_map[z, x]
        self.global_memory_map[z, x] = min(1.0, current_value + 0.1)

        # 局部记忆矩阵
        # 计算在全局地图上的源区域边界
        src_top = max(0, z - 5)
        src_bottom = min(128, z + 6)
        src_left = max(0, x - 5)
        src_right = min(128, x + 6)

        # 计算在局部地图上的目标区域边界
        dst_top = src_top - (z - 5)
        dst_bottom = src_bottom - (z - 5)
        dst_left = src_left - (x - 5)
        dst_right = src_right - (x - 5)

        # 从全局地图复制有效区域到局部地图
        self.local_memory_map[dst_top:dst_bottom, dst_left:dst_right] = self.global_memory_map[src_top:src_bottom, src_left:src_right]
        self.memory_flag = self.local_memory_map.flatten()

    def _get_pos_feature(self, found, cur_pos, target_pos):
        relative_pos = tuple(y - x for x, y in zip(cur_pos, target_pos))
        dist = np.linalg.norm(relative_pos)
        target_pos_norm = norm(target_pos, 128, -128)
        feature = np.array(
            (
                found,
                norm(relative_pos[0] / max(dist, 1e-4), 1, -1),
                norm(relative_pos[1] / max(dist, 1e-4), 1, -1),
                target_pos_norm[0],
                target_pos_norm[1],
                norm(dist, 1.41 * 128),
            ),
        )
        return feature

    def pb2struct(self, frame_state, last_action):
        obs, _ = frame_state
        self.step_no = obs["frame_state"]["step_no"]

        hero = obs["frame_state"]["heroes"][0]
        map_info = obs["map_info"]  # 解析map_info，暂未使用
        
        # 闪现是否可用 -- 新增一个字段来代表闪现是否可用，默认初始化的时候可以为True
        if hero['talent']['status'] == 0:
            self.is_flashed = False

        # 高效生成各类flag矩阵
        map_array = np.array([[v for v in row['values']] for row in map_info], dtype=np.float32)
        self.treasure_flag = (map_array == 4).astype(np.float32).flatten()
        self.end_flag = (map_array == 3).astype(np.float32).flatten()
        self.obstacle_flag = (map_array == 0).astype(np.float32).flatten()
        self.buff_flag = (map_array == 6).astype(np.float32).flatten()

        self.cur_pos = (hero["pos"]["x"], hero["pos"]["z"])

        # 更新记忆矩阵
        self.memory_update(self.cur_pos)
        '''
        message RealmOrgan {
        int32 sub_type = 1; // 物件类型，1代表宝箱, 2代表加速buff,3代表起点,4代表终点
        int32 config_id = 2; // 物件id 0代表buff，1~13代表宝箱 21代表起点, 22代表终点
        int32 status = 3; // 0表示不可获取，1表示可获取, -1表示视野外
        Position pos = 4; // 物件位置坐标
        int32 cooldown = 5;                // 物件剩余冷却时间
        RelativePosition relative_pos = 6; // 物件相对位置
        }
        '''

        # 重置organs特征矩阵
        self.organs_features.fill(0)  # 全部初始化为0

        # 遍历organs数据，进行正确的特征编码
        for organ in obs["frame_state"]["organs"]:
            config_id = organ.get("config_id", -1)
            sub_type = organ.get("sub_type", -1)
            status = organ.get("status", -1)

            # 确定存储索引
            if sub_type == 4:  # 终点
                organ_idx = 14
            elif 0 <= config_id < 14:  # 宝箱和buff
                organ_idx = config_id
            else:
                continue  # 跳过无效的organ

            # 构建正确编码的特征向量
            feature_vector = np.zeros(24, dtype=np.float32)

            # 1. sub_type one-hot编码 (3维)
            if sub_type == 1:  # treasure
                feature_vector[0] = 1.0
            elif sub_type == 2:  # buff
                feature_vector[1] = 1.0
            elif sub_type == 4:  # end
                feature_vector[2] = 1.0

            # 2. status one-hot编码 (3维)
            if status == -1:  # 视野外
                feature_vector[3] = 1.0
            elif status == 0:  # 冷却中
                feature_vector[4] = 1.0
            elif status == 1:  # 可获取
                feature_vector[5] = 1.0

            # 3. 归一化位置 (2维)
            pos_x = organ.get("pos", {}).get("x", -1)
            pos_z = organ.get("pos", {}).get("z", -1)
            if pos_x != -1 and pos_z != -1:
                feature_vector[6] = pos_x / 128.0  # 归一化到[0,1]
                feature_vector[7] = pos_z / 128.0  # 归一化到[0,1]
            # 如果位置未知，保持为0

            # 4. 归一化冷却时间 (1维)
            cooldown = organ.get("cooldown", 0)
            feature_vector[8] = min(cooldown / 100.0, 1.0)  # 归一化到[0,1]，假设最大冷却100

            # 5. relative_distance one-hot编码 (6维)
            rel_dist = organ.get("relative_pos", {}).get("l2_distance", "RELATIVE_DISTANCE_NONE")
            dist_mapping = {
                "RELATIVE_DISTANCE_NONE": 9,
                "VerySmall": 10,
                "Small": 11,
                "Medium": 12,
                "Large": 13,
                "VeryLarge": 14
            }
            if rel_dist in dist_mapping:
                feature_vector[dist_mapping[rel_dist]] = 1.0
            else:
                feature_vector[9] = 1.0  # Unknown

            # 6. relative_direction one-hot编码 (9维)
            rel_dir = organ.get("relative_pos", {}).get("direction", "North")
            dir_mapping = {
                "North": 15,
                "NorthEast": 16,
                "East": 17,
                "SouthEast": 18,
                "South": 19,
                "SouthWest": 20,
                "West": 21,
                "NorthWest": 22
            }
            if rel_dir in dir_mapping:
                feature_vector[dir_mapping[rel_dir]] = 1.0
            else:
                feature_vector[23] = 1.0  # Unknown

            # 存储编码后的特征
            self.organs_features[organ_idx] = feature_vector




        # History position
        # 历史位置
        self.history_pos.append(self.cur_pos)
        if len(self.history_pos) > 10:
            self.history_pos.pop(0)

            

        self.last_pos_norm = self.cur_pos_norm
        self.cur_pos_norm = norm(self.cur_pos, 128, -128)

        # History position feature
        # 历史位置特征
        self.feature_history_pos = self._get_pos_feature(1, self.cur_pos, self.history_pos[0])

        # 当前位置的one-hot编码特征
        # 目标位置的one-hot编码
        pos_row = [0] * 128
        pos_row[self.cur_pos[0]] = 1
        pos_col = [0] * 128
        pos_col[self.cur_pos[1]] = 1
        self.cur_pos_onehot = np.array(pos_row + pos_col, dtype=np.float32)

        self.move_usable = True
        self.last_action = last_action

    def process(self, frame_state, last_action):
        self.pb2struct(frame_state, last_action)

        # Legal action
        # 合法动作
        legal_action = self.get_legal_action()

        # Feature
        # 特征 - 使用正确编码的organs特征
        feature = np.concatenate([
            self.cur_pos_norm,                     # 2维：当前位置
            self.feature_history_pos,              # 6维：历史位置特征
            self.organs_features.flatten(),        # 360维：正确编码的organs特征 (15*24)
            self.cur_pos_onehot,                  # 256维：位置one-hot
            legal_action,                         # 16维：合法动作
            # 5个局部视野域特征 (11x11)
            self.treasure_flag.reshape(11, 11).flatten(),    # 121维：宝箱局部视野
            self.obstacle_flag.reshape(11, 11).flatten(),    # 121维：障碍物局部视野
            self.buff_flag.reshape(11, 11).flatten(),        # 121维：BUFF局部视野
            self.end_flag.reshape(11, 11).flatten(),         # 121维：终点局部视野
            self.local_memory_map.flatten()                  # 121维：记忆矩阵局部视野
        ])  # 总计1041维特征

        # 计算简化的奖励 - 基于历史位置特征
        reward = [-0.001 + min(0.001, 0.05 * self.feature_history_pos[-1])]  # 步数惩罚 + 移动奖励

        return (
            feature,
            legal_action,
            reward,
        )

    def get_legal_action(self):
        # if last_action is move and current position is the same as last position, add this action to bad_move_ids
        # 如果上一步的动作是移动，且当前位置与上一步位置相同，则将该动作加入到bad_move_ids中

        legal_action = [False] * self.move_action_num
        
        # 添加闪现的合法性
        if self.is_flashed:
            legal_action[8:] = [True] * 8
        else:
            legal_action[8:] = [False] * 8

        if (
            abs(self.cur_pos_norm[0] - self.last_pos_norm[0]) < 0.001
            and abs(self.cur_pos_norm[1] - self.last_pos_norm[1]) < 0.001
            and self.last_action > -1
        ):
            self.bad_move_ids.add(self.last_action)
        else:
            self.bad_move_ids = set()

        legal_action = [self.move_usable] * self.move_action_num
        for move_id in self.bad_move_ids:
            legal_action[move_id] = 0

        if self.move_usable not in legal_action:
            self.bad_move_ids = set()
            return [self.move_usable] * self.move_action_num

            
        return legal_action
